"""
用户输入参数解析器
支持解析用户输入中的特殊参数，如 --civitai, --seed, --lora 等
"""

import re
from typing import Dict, Any, Tuple, List, Optional
from dataclasses import dataclass


@dataclass
class ParsedParameters:
    """解析后的参数"""
    clean_prompt: str           # 清理后的提示词（移除参数）
    use_civitai: bool = False   # 是否使用Civitai搜索
    civitai_query: str = ""     # Civitai搜索关键词
    seed: Optional[int] = None  # 指定种子
    lora_names: List[str] = None  # 指定的LoRA模型名称
    lora_weights: Dict[str, float] = None  # LoRA权重
    quality: str = "standard"   # 质量级别：low, standard, high
    style: str = ""             # 风格参数
    aspect_ratio: str = ""      # 纵横比
    steps: Optional[int] = None # 生成步数
    cfg_scale: Optional[float] = None  # CFG引导强度
    use_again: bool = False     # 是否使用上一次的工作流
    no_trans: bool = False      # 是否禁用提示词润色，仅简单翻译
    raw_parameters: Dict[str, Any] = None  # 原始参数字典


class ParameterParser:
    """参数解析器"""
    
    def __init__(self):
        # 参数模式定义
        self.parameter_patterns = {
            'civitai': r'--civitai(?:\s+([^\s-]+))?',  # --civitai 或 --civitai architecture
            'seed': r'--seed\s+(\d+)',
            'lora': r'--lora\s+([^\s-]+)(?:\s+(\d*\.?\d+))?',  # --lora model_name [weight]
            'quality': r'--quality\s+(low|standard|high)',
            'style': r'--style\s+([^\s-]+)',
            'aspect_ratio': r'--(?:ar|aspect-ratio)\s+(\d+:\d+)',
            'steps': r'--steps\s+(\d+)',
            'cfg': r'--cfg\s+(\d*\.?\d+)',
            'again': r'--again(?=\s|$)',  # --again 重新生成上一次的工作流
            'repeat': r'--repeat(?=\s|$)',  # --repeat 重新生成上一次的工作流
            'retry': r'--retry(?=\s|$)',    # --retry 重新生成上一次的工作流
            'no_trans': r'--no-trans(?=\s|$)',  # --no-trans 禁用提示词润色，仅简单翻译
        }
    
    def parse_user_input(self, user_input: str) -> ParsedParameters:
        """
        解析用户输入，提取参数和清理后的提示词
        
        Args:
            user_input: 用户原始输入
            
        Returns:
            ParsedParameters: 解析结果
        """
        if not user_input:
            return ParsedParameters(clean_prompt="")
        
        # 初始化结果
        params = ParsedParameters(
            clean_prompt=user_input,
            lora_names=[],
            lora_weights={},
            raw_parameters={}
        )
        
        # 解析各种参数
        remaining_text = user_input

        # 0. 解析 --again/--repeat/--retry 参数（优先检查）
        for again_keyword in ['again', 'repeat', 'retry']:
            again_match = re.search(self.parameter_patterns[again_keyword], remaining_text, re.IGNORECASE)
            if again_match:
                params.use_again = True
                params.raw_parameters[again_keyword] = True
                # 🔥 修复：保留again参数用于显示，不从remaining_text中移除
                break  # 只匹配第一个找到的关键词

        # 0.1 解析 --no-trans 参数
        no_trans_match = re.search(self.parameter_patterns['no_trans'], remaining_text, re.IGNORECASE)
        if no_trans_match:
            params.no_trans = True
            params.raw_parameters['no_trans'] = True
            remaining_text = re.sub(self.parameter_patterns['no_trans'], '', remaining_text, flags=re.IGNORECASE)

        # 0.1 解析 --no-trans 参数
        no_trans_match = re.search(self.parameter_patterns['no_trans'], remaining_text, re.IGNORECASE)
        if no_trans_match:
            params.no_trans = True
            params.raw_parameters['no_trans'] = True
            remaining_text = re.sub(self.parameter_patterns['no_trans'], '', remaining_text, flags=re.IGNORECASE)

        # 1. 解析 --civitai 参数
        civitai_match = re.search(self.parameter_patterns['civitai'], remaining_text, re.IGNORECASE)
        if civitai_match:
            params.use_civitai = True
            params.civitai_query = civitai_match.group(1) or ""
            params.raw_parameters['civitai'] = params.civitai_query
            remaining_text = re.sub(self.parameter_patterns['civitai'], '', remaining_text, flags=re.IGNORECASE)
        
        # 2. 解析 --seed 参数
        seed_match = re.search(self.parameter_patterns['seed'], remaining_text, re.IGNORECASE)
        if seed_match:
            try:
                params.seed = int(seed_match.group(1))
                params.raw_parameters['seed'] = params.seed
                remaining_text = re.sub(self.parameter_patterns['seed'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 3. 解析 --lora 参数（可能有多个）
        lora_matches = re.finditer(self.parameter_patterns['lora'], remaining_text, re.IGNORECASE)
        for match in lora_matches:
            lora_name = match.group(1)
            lora_weight = float(match.group(2)) if match.group(2) else 0.8
            params.lora_names.append(lora_name)
            params.lora_weights[lora_name] = lora_weight
        
        if params.lora_names:
            params.raw_parameters['lora'] = params.lora_weights
            remaining_text = re.sub(self.parameter_patterns['lora'], '', remaining_text, flags=re.IGNORECASE)
        
        # 4. 解析 --quality 参数
        quality_match = re.search(self.parameter_patterns['quality'], remaining_text, re.IGNORECASE)
        if quality_match:
            params.quality = quality_match.group(1).lower()
            params.raw_parameters['quality'] = params.quality
            remaining_text = re.sub(self.parameter_patterns['quality'], '', remaining_text, flags=re.IGNORECASE)
        
        # 5. 解析 --style 参数
        style_match = re.search(self.parameter_patterns['style'], remaining_text, re.IGNORECASE)
        if style_match:
            params.style = style_match.group(1)
            params.raw_parameters['style'] = params.style
            remaining_text = re.sub(self.parameter_patterns['style'], '', remaining_text, flags=re.IGNORECASE)
        
        # 6. 解析 --aspect-ratio 参数
        ar_match = re.search(self.parameter_patterns['aspect_ratio'], remaining_text, re.IGNORECASE)
        if ar_match:
            params.aspect_ratio = ar_match.group(1)
            params.raw_parameters['aspect_ratio'] = params.aspect_ratio
            remaining_text = re.sub(self.parameter_patterns['aspect_ratio'], '', remaining_text, flags=re.IGNORECASE)
        
        # 7. 解析 --steps 参数
        steps_match = re.search(self.parameter_patterns['steps'], remaining_text, re.IGNORECASE)
        if steps_match:
            try:
                params.steps = int(steps_match.group(1))
                params.raw_parameters['steps'] = params.steps
                remaining_text = re.sub(self.parameter_patterns['steps'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 8. 解析 --cfg 参数
        cfg_match = re.search(self.parameter_patterns['cfg'], remaining_text, re.IGNORECASE)
        if cfg_match:
            try:
                params.cfg_scale = float(cfg_match.group(1))
                params.raw_parameters['cfg_scale'] = params.cfg_scale
                remaining_text = re.sub(self.parameter_patterns['cfg'], '', remaining_text, flags=re.IGNORECASE)
            except ValueError:
                pass
        
        # 清理剩余文本
        params.clean_prompt = self._clean_text(remaining_text)
        
        return params
    
    def _clean_text(self, text: str) -> str:
        """清理文本，移除多余的空格和换行"""
        if not text:
            return ""
        
        # 移除多余的空格
        cleaned = re.sub(r'\s+', ' ', text)
        
        # 移除首尾空格
        cleaned = cleaned.strip()
        
        return cleaned
    
    def format_parameters_help(self) -> str:
        """格式化参数帮助信息"""
        help_text = """
🎛️ **支持的参数**

**Civitai集成**:
  --civitai [关键词]     从Civitai搜索并下载LoRA模型
  
**模型控制**:
  --seed 12345          指定随机种子
  --lora model_name [weight]  指定LoRA模型和权重
  
**质量控制**:
  --quality low|standard|high  设置生成质量
  --steps 30            设置生成步数
  --cfg 7.5             设置CFG引导强度
  
**样式控制**:
  --style realistic     设置生成风格
  --ar 16:9             设置纵横比

**快速操作**:
  --again               重新生成上一次的图片
  --repeat              重新生成上一次的图片
  --retry               重新生成上一次的图片

**提示词控制**:
  --no-trans            仅简单翻译中文提示词，不进行润色增强

**使用示例**:
```
aigen 生成一只猫 --civitai animal --quality high --seed 12345
aigen 现代建筑 --civitai architecture --lora ASTRA_Flux_OC_Vbeta-2 0.9
aigen 人像摄影 --quality high --style realistic --ar 3:4
aigen --again          重新生成上一次的图片
aigen --repeat         重新生成上一次的图片
aigen --retry          重新生成上一次的图片
```
        """
        return help_text.strip()


# 全局参数解析器实例
parameter_parser = ParameterParser()
