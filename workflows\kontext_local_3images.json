{"6": {"inputs": {"text": "<PERSON> is a woman in black skirt, beige top.\n<PERSON> is a blonde woman in dark-gray suit, standing on the surface of Lunar. a Lunar landing module  in far distance, the blue earth hanging in the dard space.\n<PERSON> is a anime fox-ear-girl in a pink sweater and blue jeans\nPlace all three women and girl of A, B, and C into the Lunar surface setting,  they are dancing happily, while maintain everyone's face, hair, and clothes.", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["31", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "final_image_output"}}, "31": {"inputs": {"seed": 741163645721201, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["37", 0], "positive": ["35", 0], "negative": ["135", 0], "latent_image": ["188", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "35": {"inputs": {"guidance": 2.5, "conditioning": ["197", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "37": {"inputs": {"unet_name": "flux1-dev-kontext_fp8_scaled.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "38": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn_scaled.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "39": {"inputs": {"vae_name": "Flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "42": {"inputs": {"image": ["259", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "124": {"inputs": {"pixels": ["42", 0], "vae": ["39", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "135": {"inputs": {"conditioning": ["6", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "177": {"inputs": {"conditioning": ["6", 0], "latent": ["124", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "188": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "191": {"inputs": {"image": ["256", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "193": {"inputs": {"image": ["260", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "194": {"inputs": {"pixels": ["191", 0], "vae": ["39", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "195": {"inputs": {"pixels": ["193", 0], "vae": ["39", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "196": {"inputs": {"conditioning": ["177", 0], "latent": ["194", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "197": {"inputs": {"conditioning": ["196", 0], "latent": ["195", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "199": {"inputs": {"images": ["8", 0]}, "class_type": "PreviewImage", "_meta": {"title": "final_image"}}, "256": {"inputs": {"width": 1216, "height": 1216, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["262", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "259": {"inputs": {"width": 1216, "height": 1216, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["261", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "260": {"inputs": {"width": 1216, "height": 1216, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["263", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "261": {"inputs": {"base64_data": "", "image_output": "<PERSON>de", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "image_input_01"}}, "262": {"inputs": {"base64_data": "", "image_output": "<PERSON>de", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "image_input_02"}}, "263": {"inputs": {"base64_data": "", "image_output": "<PERSON>de", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "image_input_03"}}}