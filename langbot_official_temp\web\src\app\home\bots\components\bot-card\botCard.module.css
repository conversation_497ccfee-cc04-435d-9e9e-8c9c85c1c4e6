.cardContainer {
  width: 100%;
  height: 10rem;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 2px 0 rgba(0, 0, 0, 0.2);
  padding: 1.2rem;
  cursor: pointer;
}

.cardContainer:hover {
  box-shadow: 0px 2px 8px 0 rgba(0, 0, 0, 0.1);
}

.iconBasicInfoContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  gap: 0.8rem;
  user-select: none;
}

.iconImage {
  width: 4rem;
  height: 4rem;
  margin: 0.2rem;
  /* border-radius: 50%; */
}

.basicInfoContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  width: 100%;
}

.basicInfoNameContainer {
  display: flex;
  flex-direction: column;
}

.basicInfoName {
  font-size: 1.4rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.basicInfoDescription {
  font-size: 1rem;
  font-weight: 300;
  color: #b1b1b1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.basicInfoAdapterContainer {
  display: flex;
  flex-direction: row;
  gap: 0.4rem;
}

.basicInfoAdapterIcon {
  width: 1.2rem;
  height: 1.2rem;
  margin-top: 0.2rem;
  color: #626262;
}

.basicInfoAdapterLabel {
  font-size: 1.2rem;
  font-weight: 500;
  color: #626262;
}

.basicInfoPipelineContainer {
  display: flex;
  flex-direction: row;
  gap: 0.4rem;
}

.basicInfoPipelineIcon {
  width: 1.2rem;
  height: 1.2rem;
  color: #626262;
  margin-top: 0.2rem;
}

.basicInfoPipelineLabel {
  font-size: 1.2rem;
  font-weight: 500;
  color: #626262;
}

.bigText {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1.4rem;
  font-weight: bold;
  max-width: 100%;
}

.botOperationContainer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  height: 100%;
  width: 3rem;
  gap: 0.4rem;
}
