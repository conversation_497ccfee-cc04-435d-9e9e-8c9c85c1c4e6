version: "3"

services:
  langbot:
    image: docker.langbot.app/langbot-public/rockchin/langbot:latest
    container_name: langbot
    volumes:
      # 数据目录
      - ./data:/app/data
      # 插件目录
      - ./plugins:/app/plugins
      # 二次开发代码挂载（覆盖容器内的pkg目录）
      - ./pkg:/app/pkg
      # 配置文件挂载
      - ./config:/app/config
      # 工作流文件
      - ./workflows:/app/workflows
      # 模板文件
      - ./templates:/app/templates
      # 资源文件
      - ./res:/app/res
      # 临时文件和调试数据
      - ./temp:/app/temp
    restart: on-failure
    environment:
      - TZ=Asia/Shanghai
      # ComfyUI官方API Key（长期有效，无需定期更新） 
      - "API_KEY_COMFY_ORG=comfyui-ffaafa0578e9b54fc94e3bc0b1be3698ab0842e1065560ecf000eac6dd4df902"
      # FluxKontextProImageNode认证token（与API_KEY_COMFY_ORG保持一致）
      - "AUTH_TOKEN_COMFY_ORG=comfyui-ffaafa0578e9b54fc94e3bc0b1be3698ab0842e1065560ecf000eac6dd4df902"
    network_mode: host  # 使用host网络模式
    # 根据具体环境配置网络
