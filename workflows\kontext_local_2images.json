{"6": {"inputs": {"text": "Turn the rough sketch into an architectural photography, 2-story vanguard minimalist villa. maintain the structural features of the sketch.", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["31", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "final_image_output"}}, "31": {"inputs": {"seed": 190757336490356, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["37", 0], "positive": ["35", 0], "negative": ["135", 0], "latent_image": ["124", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "35": {"inputs": {"guidance": 2.5, "conditioning": ["177", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "37": {"inputs": {"unet_name": "flux1-dev-kontext_fp8_scaled.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "38": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn_scaled.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "39": {"inputs": {"vae_name": "Flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "42": {"inputs": {"image": ["146", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "124": {"inputs": {"pixels": ["42", 0], "vae": ["39", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "135": {"inputs": {"conditioning": ["6", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "146": {"inputs": {"direction": "right", "match_image_size": true, "spacing_width": 0, "spacing_color": "white", "image1": ["190", 0], "image2": ["193", 0]}, "class_type": "ImageStitch", "_meta": {"title": "Image Stitch"}}, "177": {"inputs": {"conditioning": ["6", 0], "latent": ["124", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "189": {"inputs": {"images": ["8", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "190": {"inputs": {"image": ["191", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "191": {"inputs": {"base64_data": "", "image_output": "<PERSON>de", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "image_input_01"}}, "192": {"inputs": {"base64_data": "", "image_output": "<PERSON>de", "save_prefix": "ComfyUI"}, "class_type": "easy loadImageBase64", "_meta": {"title": "image_input_02"}}, "193": {"inputs": {"image": ["192", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}}